J

javascriptS3504)Unexpected var, use let or const instead.2 @J

javascriptS3504)Unexpected var, use let or const instead.2 @?

javascriptS878!Unexpected use of comma operator.2) *J

javascriptS3504)Unexpected var, use let or const instead.2%% @]

javascriptS1126<Replace this if-then-else flow by a single return statement.2(* @J

javascriptS3504)Unexpected var, use let or const instead.2// @]

javascriptS1126<Replace this if-then-else flow by a single return statement.224 @