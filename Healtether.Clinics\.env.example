# Healtether Clinics API Environment Variables
# Copy this file to .env and fill in your actual values

# Database Configuration
MONGOOES_PASS='your_mongodb_password'
PORT='2222'
MONGODB_CONNECTION='your_mongodb_connection_string'

# JWT Configuration
JWT_SECRET='your_jwt_secret_key'

# CORS Configuration
CORS_URL='http://localhost:5173'

# Azure Storage Configuration
AZURE_CONNECTIONSTRING='your_azure_storage_connection_string'
CLINICBLOB_CONTAINER_PREFIX='clinic-'
PATIENT_BLOB_FOLDER='patient/'
VITE_BLOB_URL='https://your_storage_account.blob.core.windows.net/'

# Environment
ENV='dev'

# Google Meet Configuration
GOOGLE_MEET_ADMIN_EMAIL="<EMAIL>"

# CloudWatch Logging Configuration
CLOUD_WATCH_LOG_GROUP="your_log_group"
CLOUD_WATCH_LOG_STREAM="your_log_stream"
CLOUD_WATCH_ACCESSKEY="your_aws_access_key"
CLOUD_WATCH_SECRET="your_aws_secret_key"
CLOUD_WATCH_REGION="ap-south-1"

# WhatsApp API Configuration
WHATSAPP_API_URL='http://localhost:3001/api'

# Payment Gateway Configuration
PHONEPE_CREATE_LINK='https://api.phonepe.com/apis/hermes/pg/v1/pay'
PHONEPE_REDIRECT='https://www.healtether.com/pgResponse'
PHONEPE_CALLBACK='your_callback_url'

# SNOMED Server Configuration
SNOMED_SERVER_BASE_URL='your_snomed_server_url'

# ABHA Configuration
HIP_ID="your_hip_id"
APPOINTMENT_DURATION_MINUTES=30

# Firebase Service Account Credentials
FIREBASE_TYPE="service_account"
FIREBASE_PROJECT_ID="your_firebase_project_id"
FIREBASE_PRIVATE_KEY_ID="your_firebase_private_key_id"
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_firebase_private_key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL="your_firebase_client_email"
FIREBASE_CLIENT_ID="your_firebase_client_id"
FIREBASE_AUTH_URI="https://accounts.google.com/o/oauth2/auth"
FIREBASE_TOKEN_URI="https://oauth2.googleapis.com/token"
FIREBASE_AUTH_PROVIDER_X509_CERT_URL="https://www.googleapis.com/oauth2/v1/certs"
FIREBASE_CLIENT_X509_CERT_URL="your_firebase_client_x509_cert_url"
FIREBASE_UNIVERSE_DOMAIN="googleapis.com"

# Google Console Service Account Credentials
GOOGLE_TYPE="service_account"
GOOGLE_PROJECT_ID="your_google_project_id"
GOOGLE_PRIVATE_KEY_ID="your_google_private_key_id"
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_google_private_key\n-----END PRIVATE KEY-----\n"
GOOGLE_CLIENT_EMAIL="your_google_client_email"
GOOGLE_CLIENT_ID="your_google_client_id"
GOOGLE_AUTH_URI="https://accounts.google.com/o/oauth2/auth"
GOOGLE_TOKEN_URI="https://oauth2.googleapis.com/token"
GOOGLE_AUTH_PROVIDER_X509_CERT_URL="https://www.googleapis.com/oauth2/v1/certs"
GOOGLE_CLIENT_X509_CERT_URL="your_google_client_x509_cert_url"
GOOGLE_UNIVERSE_DOMAIN="googleapis.com"
