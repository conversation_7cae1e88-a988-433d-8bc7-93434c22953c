import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import mongoose from "mongoose";
import auth from "./routes/auth/auth.routes.js";
// import AdminRouter from "./router/admin/adminRouter.js"; import ChatBotRouter
// from './router/bot/BotRouter.js'
import { mongodb } from "./config/mongodb.config.js";
import staff from "./routes/staff/staff.routes.js";
import patient from "./routes/patient/patient.routes.js";
import notification from "./routes/notification/notification.routes.js";
import appointment from "./routes/appointment/appointment.routes.js";
import payment from "./routes/payment/payment.routes.js";
import client from "./routes/clinic/client.routes.js";
import { SetFirebaseNotification } from "./config/firebase.admin.js";
import analyatic from "./routes/analyatics/analyatics.routes.js";
import { initGoogleCalendarClient } from "./config/googleAuthConection.js";
import writePrescription from "./routes/appointment/write-prescription.routes.js";
import frequency from "./routes/frequency/frequency.routes.js";
import abha from "./routes/abha/abha.routes.js";
import abhaM3 from "./routes/abha/abha.m3.routes.js"; // Import the new route

import errorHandler from "./middleware/ErrorHandler.js";
import { swaggerUi, swaggerSpec } from './config/swaggerConfig.js';
import { initializeAppInsights } from "./config/appLogger.js";
import bookedConsultation from "./routes/bookedConsultation/booked-consultation.routes.js";
import cron from 'node-cron';
import { getAllClinics } from "./controllers/clinic/client.controller.js";
import { resetDailyCounter } from "./utils/common.utils.js";
import { securityHeaders, apiSecurityHeaders, securityLogging, secure404Handler } from "./middleware/security.middleware.js";

dotenv.config();
initializeAppInsights();
const app = express();

// Disable Express.js version disclosure for security
app.disable('x-powered-by');

const port = process.env.PORT;

// Security middleware - Apply first for all requests
app.use(securityHeaders);
app.use(securityLogging);

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

//cors connecting
app.use(
  cors({
    origin: process.env.CORS_URL,
    methods: ["GET", "POST", "PUT", "PATCH", "DELETE"],
    credentials: true,
  })
);
SetFirebaseNotification();
//database connecting
mongodb(process.env.MONGODB_CONNECTION);
app.get("/", (req, res) => {
  res.send("Healtether.Portal.Api running !!!");
});

// Apply additional API security headers for all API routes
app.use("/api/*", apiSecurityHeaders);

app.use("/api/", auth);
app.use("/api/staff", staff);
app.use("/api/clinic", client);
app.use("/api/patient", patient);
app.use("/api/analyatic", analyatic);
app.use("/api/notification", notification);
app.use("/api/appointment", appointment);
app.use("/api/appointment/write-prescription", writePrescription);
app.use("/api/payment", payment);
app.use("/api/frequency", frequency);
app.use("/api/abha", abha);
app.use("/api/abha/m3", abhaM3);
app.use("/api/booked-consultation", bookedConsultation);
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
app.post("/api/paid", (req, res) => {
  console.log("res pay:", JSON.stringify(req.body));
  res.send("paid");
});

// Reset counters every day at midnight for all clinics
cron.schedule('0 0 * * *', async () => {
  try {
    const clinics = await getAllClinics();
    for (const clinic of clinics) {
      await resetDailyCounter(clinic._id);
    }
    console.log('Daily token counters reset for all clinics');
  } catch (error) {
    console.error('Error resetting daily counters:', error);
  }
});

// Handle 404 responses with proper security headers
app.use(secure404Handler);

app.use(errorHandler);
const server = app.listen(port, () => {
  console.log(`Portal Api Server Started running port ${port}...`);
});


// Used Graceful shutdown handling
const shutdown = async () => {
  console.log("Shutdown initiated...");
  process.env.SHUTTING_DOWN = true;

  server.close(async () => {
    console.log("HTTP server closed.");

  try { 
    await mongoose.connection.close(false);
    console.log("MongoDB connection closed.");
    process.exit(0);
  } catch (err) {
    console.error("Error closing MongoDB connection:", err);
  }
    process.exit(1);
  });
};

process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);

// Check and remove
(async () => {
  try {
    await initGoogleCalendarClient();
  } catch (error) {
    console.error('Failed to initialize Google client:', error);
  }
})();

export {app,server};