import { checkSchema, validationResult } from "express-validator";
import mongoose from "mongoose";

export const validateGetPatientOverview = async (req, res, next) => {
  await checkSchema({
    clientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Client ID must be a valid ObjectId",
      },
      exists: {
        errorMessage: "Client ID is required",
      },
    },
    page: {
      trim: true,
          escape: true,
      in: ["query"],
      optional: true,
      isInt: {
        options: { min: 0 },
        errorMessage: "Page number must be a non-negative integer",
      },
    },
    size: {
      trim: true,
          escape: true,
      in: ["query"],
      optional: true,
      isInt: {
        options: { min: 1 },
        errorMessage: "Size must be a positive integer",
      },
    },
    keyword: {
      trim: true,
          escape: true,
      in: ["query"],
      isString: true,
      errorMessage: "Keyword must be a string",
      optional: true,
    },
    sortby: {
      trim: true,
          escape: true,
      in: ["query"],
      isString: true,
      errorMessage: "Sort by field must be a string",
      optional: true,
    },
    direction: {
      in: ["query"],
      isIn: {
        options: [["asc", "desc"]],
        errorMessage: 'Sort direction must be either "asc" or "desc"',
      },
      optional: true,
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetPatient = async (req, res, next) => {
  await checkSchema({
    id: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Patient ID must be a valid ObjectId",
      },
      exists: {
        errorMessage: "Patient ID is required",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateSearchPatientMobile = async (req, res, next) => {
  await checkSchema({
    mobile: {
      trim: true,
          escape: true,
      in: ["query"],
      isString: true,
      errorMessage: "Mobile must be a string",
      exists: {
        errorMessage: "Mobile is required",
      },
    },
    clinicId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
      exists: {
        errorMessage: "Clinic ID is required",
      },
    },
    size: {
      trim: true,
          escape: true,
      in: ["query"],
      isInt: {
        options: { min: 1 },
        errorMessage: "Size must be a positive integer",
      },
      optional: true,
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateAddOrUpdatePatientDetails = async (req, res, next) => {
  await checkSchema({
    "patientData.firstName": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "First name must be a string",
      exists: {
        errorMessage: "First name is required",
      },
    },
    "patientData.lastName": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Last name must be a string",
      exists: {
        errorMessage: "Last name is required",
      },
    },
    "patientData.age": {
      trim: true,
          escape: true,
      in: ["body"],
      isInt: {
        options: { min: 1, max: 100 },
        errorMessage: "Age must be an integer between 1 and 100",
      },
      optional: true,
    },
    "patientData.height": {
      trim: true,
          escape: true,
      in: ["body"],
      isNumeric: true,
      errorMessage: "Height must be a number",
      optional: true,
    },
    "patientData.weight": {
      trim: true,
          escape: true,
      in: ["body"],
      isNumeric: true,
      errorMessage: "Weight must be a number",
      optional: true,
    },
    "patientData.birthday": {
      in: ["body"],
      optional: true,
      custom: {
        options: (value) => {
          return value === "" || !value || !isNaN(new Date(value).getTime());
        },
      },
      errorMessage: "Birthday must be a valid date",
  },
    "patientData.gender": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Gender must be a string",
      optional: true,
    },
    "patientData.mobile": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Mobile number must be a string",
      exists: {
        errorMessage: "Mobile number is required",
      },
    },
    "patientData.whatsapp": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "WhatsApp number must be a string",
      optional: true,
    },
    "patientData.countryCode": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Country code must be a string",
      optional: true,
    },
    "patientData.email": {
      trim: true,
          escape: true,
      in: ["body"],
      optional: {
        options: { checkFalsy: true }, // Skip validation if the field is an empty string
      },
      isEmail: {
        errorMessage: "Must be a valid email address",
      },
      isLength: {
        options: { max: 100 },
        errorMessage: "Email must be at most 100 characters long",
      },
    },
    "patientData.address.house": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "House must be a string",
      optional: true,
    },
    "patientData.address.street": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Street must be a string",
      optional: true,
    },
    "patientData.address.landmarks": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Landmarks must be a string",
      optional: true,
    },
    "patientData.address.city": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "City must be a string",
      optional: true,
    },
    "patientData.address.pincode": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Pincode must be a string",
      optional: true,
    },
    "patientData.documentType": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Document type must be a string",
      optional: true,
    },
    "patientData.documentNumber": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Document number must be a string",
      optional: true,
    },
    "patientData.created.on": {
      trim: true,
          escape: true,
      in: ["body"],
      isDate: true,
      errorMessage: "Created On must be a valid date",
      optional: true,
    },
    "patientData.modified.on": {
      trim: true,
          escape: true,
      in: ["body"],
      isDate: true,
      errorMessage: "Modified On must be a valid date",
      optional: true,
    },
    "patientData.createdBy.id": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Created By ID must be a string",
      optional: true,
    },
    "patientData.createdBy.name": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Created By Name must be a string",
      optional: true,
    },
    "patientData.modifiedBy.id": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Modified By ID must be a string",
      optional: true,
    },
    "patientData.modifiedBy.name": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Modified By Name must be a string",
      optional: true,
    },
    "patientData.documents.*.fileName": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Document file name must be a string",
      optional: true,
    },
    "patientData.documents.*.blobName": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Document blob name must be a string",
      optional: true,
    },
    "patientData.documents.*.uploadedOn": {
      trim: true,
          escape: true,
      in: ["body"],
      isDate: true,
      errorMessage: "Uploaded On must be a valid date",
      optional: true,
    },
    "patientData.deleted": {
      trim: true,
          escape: true,
      in: ["body"],
      isBoolean: true,
      errorMessage: "Deleted must be a boolean value",
      optional: true,
    },
    "patientData.clinic": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
      optional: true,
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateDeletePatient = async (req, res, next) => {
  await checkSchema({
  id: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Patient ID must be a valid ObjectId",
      },
      exists: {
        errorMessage: "Patient ID is required",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetPatientAppointmentOrMedical = async (
  req,
  res,
  next
) => {
  await checkSchema({
    "id": {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Patient ID must be a valid ObjectId",
      },
      exists: {
        errorMessage: "Patient ID is required",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateUpdateDocumentOrRecords = async (req, res, next) => {
  await checkSchema({
    "clientId": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Client ID must be a valid ObjectId",
      },
      exists: {
        errorMessage: "Client ID is required",
      },
    },
    "documents": {
      trim: true,
          escape: true,
      in: ["files"],
      optional: true,
      errorMessage: "Documents must be uploaded",
    },
    // Add other file fields as needed
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};
