import moment from "moment";
import { convertData } from "../controllers/appointments/write-prescription.controller.js";
import { getAppointment } from "../helpers/appointment/appointment.helper.js";
import { getMedicalHistoryForPatient } from "../helpers/appointment/write-prescription/medical-histories.helper.js";
import { getPrescriptionAndVitalsForAppointment } from "../helpers/appointment/write-prescription/prescription.helper.js";
import { getClientById } from "../helpers/clinic/client.helper.js";
import { getPatientDetail } from "../helpers/patient/patient.helper.js";
import { staffById } from "../helpers/staff/staff.helper.js";
import { Invoice } from "../model/clinics.model.js";
import { fetchImageAsBase64 } from "./common.utils.js";
import fs from "fs";
import path from "path";
// Function to create general details
const createGeneralDetails = (artifact, hipUrl, hipIds, status, clientId) => ({
  artifact: artifact || "OPConsultRecord",
  hipUrl: hipUrl || "https://www.healtether.com",
  hipIds: hipIds || ["hpi1", "hip2"],
  status: status || "final",
  clientId: clientId || "SBX_003515",
});

// Function to create patient details
const createPatientDetails = (
  id,
  abhaNumber,
  abhaAddress,
  name,
  gender,
  dob,
  address,
  doctors,
  allergyIntolerances,
  telecom
) => {
  // console.log("address", address.landmarks);
  return {
    id: id || "3434545",
    abhaNumber: abhaNumber || "91-1248-5708-0632",
    abhaAddress: abhaAddress || "monikakushwah12@sbx",
    name: name || { text: "monika kushwah", prefix: ["Ms"] },
    gender: gender?.toLowerCase(),
    dob: moment(dob).format("YYYY-MM-DD") || "2003-12-05",
    address: [
      {
        use: "home",
        type: "physical",
        postalCode: address.pincode,
        country: "india",
        district: address.landmarks,
        city: address.city,
        state: address.landmarks,
        text: address.house,
      },
    ] : [
      {
        use: "home",
        type: "physical",
        text: "dummy address line, dummy city, dummy district, dummy state",
        line: ["dummy address line", "1", "2", "3"],
        city: "dummy city",
        district: "dummy district",
        state: "dummy state",
        postalCode: "625513",
        country: "india",
        period: {
          start: "2025-02-06T14:15:51.156+05:30",
          end: "2025-02-06T14:15:51.156+05:30",
        },
      },
    ],
    doctors: doctors || ["Dr. Smith"],
    allergyIntolerances: Array.isArray(allergyIntolerances)
      ? allergyIntolerances?.map((item) => ({
          type: item.name,
          clinicalStatus: "active",
          verificationStatus: "confirmed",
          notes: [item.notes], // Wrapping notes in an array
          doctor: doctors[0], // Assuming doctor is constant, update dynamically if needed
        }))
      : [
          {
            type: "peanut",
            clinicalStatus: "active",
            verificationStatus: "confirmed",
            notes: ["Allergic to penicillin"],
            doctor: "Dr. Smith",
          },
        ],
    telecom: [
      {
        system: "phone",
        value: telecom || "+91-**********",
        use: "mobile",
      },
    ],
  };
};

const createPractitionerDetails = (practitionerData,patientId) => {
  // console.log("address", practitionerData.address);
  return {
    names: practitionerData.firstName && practitionerData.lastName
      ? [`${practitionerData.firstName + practitionerData.lastName}`]
      : ["Dr. Smith"],
    licenses: [
      {
        code: "MD",
        display: "Medical License number",
        licNo: practitionerData.hprId || "123",
      },
    ],
    telecom: [
      {
        system: "phone",
        value: practitionerData.mobile || "+91-**********",
        use: "mobile",
      },
    ],
    gender: practitionerData.gender?.toLowerCase(),
    birthDate:
      moment(practitionerData.birthDate).format("YYYY-MM-DD") || "1993-10-03",
    patient:patientId||"patient123",
    address: practitionerData.address ? [
      {
        use: "home",
        type: "physical",
        postalCode: practitionerData.address.pincode,
        country: "india",
        district: practitionerData.address.landmarks,
        city: practitionerData.address.city,
        state: practitionerData.address.landmarks,
        text: practitionerData.address.house,
      },
    ] : [
      {
        use: "home",
        type: "physical",
        text: "dummy address line, dummy city, dummy district, dummy state",
        line: ["dummy address line", "1", "2", "3"],
        city: "dummy city",
        district: "dummy district",
        state: "dummy state",
        postalCode: "625513",
        country: "india",
        // period: {
        //   start: new Date().toISOString(),
        //   end: new Date().toISOString(),
        // },
      },
    ],
  };
};
const createChargeItems = (type, quantity,id) => ({
 id:id,
  type: type || "consultation",
  status: "billed",
  quantity: quantity || 1,
});

const createInvoiceDetails = (
  id,
  status,
  date,
  totalNet,
  totalGross,
  invoice
) => ({
  id: id || "3434545",
  status: status || "issued",
  date: date || new Date().toISOString().split("T")[0],
  totalNet: totalNet || { value: 2752, currency: "INR" },
  totalGross: totalGross || { value: 2575, currency: "INR" },
  lineItem:  invoice.treatments.length>0&&invoice.treatments.map((treatment)=>{
    return {
      type: treatment.treatment,
      priceComponent: [
        { type: "base", amount: { value:parseFloat(invoice.totalTax.toString()), currency: "INR" } },
        { type: "discount", amount: { value: parseFloat(invoice.discount.toString()) , currency: "INR" } },
        {
          type: "tax",
          display: "CGST",
          amount: { value: parseFloat(treatment.cgstAmount.toString()) , currency: "INR" },
        },
        {
          type: "tax",
          display: "SGST",
          amount: { value:parseFloat(treatment.sgstAmount.toString()) , currency: "INR" },
        },
      ],
    }
  })
});

const serviceRequests = (status, intent, category, type) => ({
  status: status || "completed",
  intent: intent || "order",
  categories: category || ["Blood Test"],
  type: type || "laboratory Test",
});
const createCondition = (type) => ({
  type: type || "Hypertension",
  status: "active",
  recordedDate: new Date().toISOString(),
  startDate: new Date().toISOString(),
  endDate: new Date().toISOString(),
});

const createMedicationStatement = (status, type) => ({
  status: status || "completed",
  type: type || "Telmisartan 20 mg oral tablet",
});

const createMedicationRequest = (drugs, diagnosis, symptoms) => ({
  status: "active",
  intent: "order",
  authoredOn: new Date().toISOString(),
  medication: drugs.name || "Paracetamol",
  forCondition: diagnosis.map((diagnosis) => diagnosis.name),
  reason: symptoms && symptoms.length > 0
    ? symptoms.map((symptom) => symptom.name)
    : ["Pain relief", "Traveler's diarrhea"],
  dosageInstruction: [
    {
      text: diagnosis.notes || "500mg every 6 hours",
      repeat: {
        frequency: drugs.frequency,
        period: drugs.duration.value,
        periodUnit: drugs.duration.unit,
      },
      route: "Oral",
      doseQuantity: {
        value: drugs.dosage,
        unit: "tablet",
      },
      site: "Mouth",
      additionalInstruction: drugs.content || [
        "Take with water",
        "Do not crush or chew",
      ],
    },
  ],
});
// Function to create encounter details
const createEncounterDetails = (appointmentData) => {
  return {
    status: "finished",
    startTime: appointmentData?.created?.on || "2023-10-01T10:00:00+05:30",
    endTime: appointmentData?.ended?.on || "2023-11-01T10:00:00+05:30",
  };
};

// Function to create organization details
const createOrganizationDetails = (clinicData) => ({
  name: "Health Organization",
  telecom: [
    {
      system: "phone",
      value: clinicData.adminUserId.mobile || "+91-**********",
      use: "work",
    },
  ],
  licenses: [
    {
      code: "PRN",
      display: "Provider number",
      licNo: clinicData.hfrId || "SBX_003515",
    },
  ],
});

// Function to create procedure details
const createProcedure = (status, type, performedDateTime, followUp) => ({
  status: status || "completed",
  type: type || "Electrocardiogram",
  performedDateTime: performedDateTime || new Date().toISOString(),
  followUp:  ["Follow-up consultation"],
});
const createAppointmentDetails = (appointmentData, prescription) => ({
  status: "booked",
  serviceCategories: ["Consultation"],
  serviceTypes: ["General"],
  specialty: appointmentData.speciality ? [appointmentData.speciality] : ["Cardiology", "Anesthetics"],
  appointmentType: "consultation",
  description: appointmentData.reason || "Follow-up consultation",
  start: appointmentData.started.on || "2023-10-01T10:00:00+05:30",
  end: appointmentData.started.ended || "2023-10-01T11:00:00+05:30",
  created: appointmentData.created.on || "2023-09-30T10:00:00+05:30",
  reasonReference: prescription.prescriptions.diagnosis && prescription.prescriptions.diagnosis.length > 0
    ? prescription.prescriptions.diagnosis.map((symptom) => symptom.name)
    : ["Hypertension"],
  basedOnServices: prescription.prescriptions.labTests && prescription.prescriptions.labTests.length > 0
    ? prescription.prescriptions.labTests.map((labTest) => labTest.name)
    : ["Blood Test"],
});

const createDocumentReference = async (content) => {
  const base64Data = await fetchImageAsBase64(content);
  return {
    status: "current",
    docStatus: "final",
    type: "Clinical consultation report",
    content: [
      {
        attachment: {
          contentType: "application/pdf",
          language: "en",
          data: base64Data,
          title: "Consultation Report - John Doe",
          creation: new Date().toISOString(),
        },
      },
    ],
  };
};
// Function to create signature details
const createSignatureDetails = (data, doctor) => ({
  who: { type: "Practitioner", name: doctor[0] || "Dr. Smith" },
  sigFormat: "image/jpeg",
  data: data || "c2lnbmF0dXJlIGRhdGEgaGVyZQ==",
});

const mapVitalsToObservations = (vitals) => {
  const vitalMappings = {
    bloodPressure: {
      systolic: {
        code: "8480-6",
        display: "Systolic Blood Pressure",
        unit: "mmHg",
        unitCode: "mm[Hg]",
      },
      diastolic: {
        code: "8462-4",
        display: "Diastolic Blood Pressure",
        unit: "mmHg",
        unitCode: "mm[Hg]",
      },
    },
    height: {
      code: "8302-2",
      display: "Body Height",
      unit: "in",
      unitCode: "[in_i]",
    },
    pulseRate: {
      code: "8867-4",
      display: "Heart Rate",
      unit: "beats/minute",
      unitCode: "/min",
    },
    rbs: {
      code: "2339-0",
      display: "Glucose [Mass/volume] in Blood",
      unit: "mg/dL",
      unitCode: "mg/dL",
    },
    respiratoryRate: {
      code: "9279-1",
      display: "Respiratory Rate",
      unit: "breaths/min",
      unitCode: "/min",
    },
    spo2: {
      code: "2708-6",
      display: "Oxygen saturation in Arterial blood",
      unit: "%",
      unitCode: "%",
    },
    temperature: {
      code: "8310-5",
      display: "Body Temperature",
      unit: "Cel",
      unitCode: "Cel",
    },
  };

  const effectiveDateTime = new Date().toISOString(); // Current timestamp

  let observations = [];

  // Map blood pressure separately
  if (vitals.bloodPressure) {
    observations.push({
      bloodPressure: [
        {
          status: "final",
          code: {
            code: vitalMappings.bloodPressure.systolic.code,
            display: vitalMappings.bloodPressure.systolic.display,
          },
          valueQuantity: {
            value: vitals.bloodPressure.systolic,
            unit: vitalMappings.bloodPressure.systolic.unit,
            code: "mm[Hg]",
          },
          effectiveDateTime,
        },
        {
          status: "final",
          code: {
            code: vitalMappings.bloodPressure.diastolic.code,
            display: vitalMappings.bloodPressure.diastolic.display,
          },
          valueQuantity: {
            value: vitals.bloodPressure.diastolic,
            unit: vitalMappings.bloodPressure.diastolic.unit,
            code: "mm[Hg]",
          },
          effectiveDateTime,
        },
      ],
    });
  }

  // Map other vitals
  Object.keys(vitalMappings).forEach((key) => {
    // Skip bloodPressure since it's already handled
    if (key !== "bloodPressure" && vitals[key] !== undefined) {
      observations.push({
        status: "final",
        code: {
          code: vitalMappings[key].code,
          display: vitalMappings[key].display,
        },
        valueQuantity: {
          value: vitals[key],
          unit: vitalMappings[key].unit,
          code: vitalMappings[key].unitCode, // Using unit as code
        },
        effectiveDateTime,
      });
    }
  });

  return observations;
};
const diagnosticReport = () => ({
  status: "final",
  issued: "2023-10-01T10:30:00+05:30",
  encounter: "encounter id",
  result: [
    {
      id: "observation-id-1",
      display: "Observation/Cholesterol",
    },
  ],
  conclusion: "All tests are normal.",
  categories: ["Blood Test"],
  type: "laboratory Test",
  identifier: {
    system: "https://www.healtether.com/lab",
    value: "32432432",
  },
  presentedForm: [
    {
      attachment: {
        contentType: "application/pdf",
        language: "en",
        data: "VGhpcyBpcyBhIGR1bW15IEJhc2U2NCBlbmNvZGVkIGRvY3VtZW50Lg==",
        title: "Consultation Report - John Doe",
        creation: "2023-10-01T10:30:00+05:30",
      },
    },
  ],
});

// Function to assemble the complete structure
export const createStructuredData = async (
  artifact,
  clinicData,
  patient,
  appointmentData,
  practitionerData,
  prescription,
  medicalHistory
) => {
  const allDocuments = [
    ...appointmentData.medicalRecords,
    ...appointmentData.procedureRecords,
    ...appointmentData.prescriptionRecords,
  ];
  const documentReferencePromises =
    allDocuments.length > 0
      ? allDocuments
          .filter((item) => item.blobName) // Ensure blobName exists
          .map((item) => {
            const url = `https://devuhi.blob.core.windows.net/clinic-662ca0a41a2431e16c41ebaa/patient/${item.blobName}`;
            console.log("Fetching image from:", url); // Debugging step
            return createDocumentReference(url);
          })
      : [];

  // Wait for all document reference promises to resolve
  const documentReferences = await Promise.all(documentReferencePromises);

  return {
    general: createGeneralDetails(
      artifact,
      "https://www.healtether.com",
      ["hip1", "hip2"],
      "final",
      "SBX_003515"
    ),
    patient: createPatientDetails(
      patient._id,
      patient.abhaNumber,
      patient.abhaAddress,
      {
        text: `${patient.firstName + patient.lastName}`,
        prefix: [`${patient.prefix}`],
      },
      patient.gender,
      patient.birthday,
      patient?.address,
      [`${practitionerData.firstName + practitionerData.lastName}`],
      medicalHistory.allergies,
      patient.mobile
    ),
    encounter: createEncounterDetails(appointmentData),
    organization: createOrganizationDetails(clinicData),
    appointment: createAppointmentDetails(appointmentData, prescription),
    practitioners: [createPractitionerDetails(practitionerData,patient._id)],
    serviceRequests: prescription.prescriptions.labTests.map((labtest) => {
      return serviceRequests("completed", "order", [`${labtest.name}`]);
    }),
    conditions: prescription.prescriptions.diagnosis.map((diagnosis) => {
      return createCondition(diagnosis.name);
    }),
    medicationStatements: [createMedicationStatement()],
    medicationRequests: prescription.prescriptions.drugPrescriptions.map(
      (drugs) => {
        return createMedicationRequest(
          drugs,
          prescription.prescriptions.diagnosis,
          prescription.prescriptions.symptoms
        );
      }
    ),
    procedures: medicalHistory.pastProcedureHistory.map((procedure) => {
      return createProcedure("completed", procedure.name,"2023-10-01T10:30:00+05:30");
    }),
    documentReferences: documentReferences,
    signature: createSignatureDetails("", [
      `${practitionerData.firstName + practitionerData.lastName}`,
    ]),
  };
};

export const createDischargeSummaryStructuredData = (
  artifact,
  clinicData,
  patient,
  appointmentData,
  practitionerData,
  prescription,
  medicalHistory
) => {
 

  return {
    general: createGeneralDetails(
      artifact,
      "https://www.healtether.com",
      ["hip1", "hip2"],
      "final",
      "SBX_003515"
    ),
    patient: createPatientDetails(
      patient._id,
      patient.abhaNumber,
      patient.abhaAddress,
      {
        text: `${patient.firstName + patient.lastName}`,
        prefix: [`${patient.prefix}`],
      },
      patient.gender,
      patient.birthday,
      patient?.address,
      [`${practitionerData.firstName + practitionerData.lastName}`],
      medicalHistory.allergies,
      patient.mobile
    ),
    encounter: createEncounterDetails(),
    organization: createOrganizationDetails(clinicData),
    appointment: createAppointmentDetails(appointmentData, prescription),
    practitioners: [createPractitionerDetails(practitionerData)],
    serviceRequests: prescription.prescriptions.labTests.map((labtest) => {
      return serviceRequests("completed", "order", [`${labtest.name}`]);
    }),
    conditions: prescription.prescriptions.diagnosis.map((diagnosis) => {
      return createCondition(diagnosis.name);
    }),
    medicationStatements: [createMedicationStatement()],
    medicationRequests: prescription.prescriptions.drugPrescriptions.map(
      (drugs) => {
        return createMedicationRequest(
          drugs,
          prescription.prescriptions.diagnosis,
          prescription.prescriptions.symptoms
        );
      }
    ),
    procedures: medicalHistory.pastProcedureHistory.map((procedure) => {
      return createProcedure("completed", "procedure", procedure.name);
    }),
    dischargeSummary: [],
    signature: createSignatureDetails("", [
      `${practitionerData.firstName + practitionerData.lastName}`,
    ]),
  };
};

const createBinaryDetails = ( type,
  data) => ({
  contentType:type,
  data:data,
});

export const createPrescriptionStructuredData = async (
  artifact,
  clinicData,
  patient,
  appointmentData,
  practitionerData,
  prescription,
  medicalHistory
) => {

  const base64Data = await fetchImageAsBase64(`https://devuhi.blob.core.windows.net/clinic-662ca0a41a2431e16c41ebaa/patient/${appointmentData.prescriptionReport[0].blobName}`);
  return {
    general: createGeneralDetails(
      artifact,
      "https://www.healtether.com",
      ["hip1", "hip2"],
      "final",
      "SBX_003515"
    ),
    patient: createPatientDetails(
      patient._id,
      patient.abhaNumber,
      patient.abhaAddress,
      {
        text: `${patient.firstName + patient.lastName}`,
        prefix: [`${patient.prefix}`],
      },
      patient.gender,
      patient.birthday,
      patient?.address,
      [`${practitionerData.firstName + practitionerData.lastName}`],
      medicalHistory.allergies,
      patient.mobile
    ),
    encounter: createEncounterDetails(appointmentData),
    organization: createOrganizationDetails(clinicData),
    practitioners: [createPractitionerDetails(practitionerData)],
    conditions: prescription.prescriptions.diagnosis.map((diagnosis) => {
      return createCondition(diagnosis.name);
    }),
    medicationRequests: prescription.prescriptions.drugPrescriptions.map(
      (drugs) => {
        return createMedicationRequest(
          drugs,
          prescription.prescriptions.diagnosis,
          prescription.prescriptions.symptoms
        );
      }
    ),
    procedures: medicalHistory.pastProcedureHistory.map((procedure) => {
      return createProcedure("completed", "procedure", procedure.name);
    }),
    binary: createBinaryDetails("application/pdf" , base64Data),
    signature: createSignatureDetails("", [
      `${practitionerData.firstName + practitionerData.lastName}`,
    ]),
  };
};

export const createHealthRecordStructuredData = async (
  artifact,
  clinicData,
  appointmentData,
  patient,
  practitionerData
) => {
  const allDocuments = [
    ...(appointmentData.medicalRecords || []),
    ...(appointmentData.procedureRecords || []),
    ...(appointmentData.prescriptionRecords || []),
  ];
  const documentReferencePromises =
    allDocuments.length > 0
      ? allDocuments
          .filter((item) => item.blobName) // Ensure blobName exists
          .map((item) => {
            const url = `https://devuhi.blob.core.windows.net/clinic-662ca0a41a2431e16c41ebaa/patient/${item.blobName}`;
            console.log("Fetching image from:", url); // Debugging step
            return createDocumentReference(url);
          })
      : [];

  // Wait for all document reference promises to resolve
  const documentReferences = await Promise.all(documentReferencePromises);
  return {
    general: createGeneralDetails(
      artifact,
      "https://www.healtether.com",
      ["hip1", "hip2"],
      "final",
      "SBX_003515"
    ),
    patient: createPatientDetails(
      patient._id,
      patient.abhaNumber,
      patient.abhaAddress,
      {
        text: `${patient.firstName + patient.lastName}`,
        prefix: [`${patient.prefix}`],
      },
      patient.gender,
      patient.birthday,
      patient?.address,
      [`${practitionerData.firstName + practitionerData.lastName}`],
      [],
      patient.mobile
    ),
    organization: createOrganizationDetails(clinicData),
    practitioners: [createPractitionerDetails(practitionerData)],

    documentReferences: documentReferences,
    signature: createSignatureDetails("", [
      `${practitionerData.firstName + practitionerData.lastName}`,
    ]),
  };
};

export const createWellnessStructuredData = (
  artifact,
  patient,
  practitionerData,
  prescription,
  medicalHistory
) => ({
  general: createGeneralDetails(
    artifact,
    "https://www.healtether.com",
    ["hip1", "hip2"],
    "final",
    "SBX_003515"
  ),
  patient: createPatientDetails(
    patient._id,
    patient.abhaNumber,
    patient.abhaAddress,
    {
      text: `${patient.firstName + patient.lastName}`,
      prefix: [`${patient.prefix}`],
    },
    patient.gender,
    patient.birthday,
    patient?.address,
    [`${practitionerData.firstName + practitionerData.lastName}`],
    medicalHistory?.allergies,
    patient.mobile
  ),
  practitioners: [createPractitionerDetails(practitionerData)],
  observations: mapVitalsToObservations(prescription.vitals),

  signature: createSignatureDetails("", [
    `${practitionerData.firstName + practitionerData.lastName}`,
  ]),
});
export const createStructureForInvoice = async (
  artifact,
  clinicData,
  patient,
  practitionerData,
  invoiceData
) => {
  return {
    general: createGeneralDetails(
      artifact,
      "https://www.healtether.com",
      ["hip1", "hip2"],
      "final",
      "SBX_003515"
    ),
    patient: createPatientDetails(
      patient._id,
      patient.abhaNumber,
      patient.abhaAddress,
      {
        text: `${patient.firstName + patient.lastName}`,
        prefix: [`${patient.prefix}`],
      },
      patient.gender,
      patient.birthday,
      patient?.address,
      [`${practitionerData.firstName + practitionerData.lastName}`],
      patient.mobile
    ),
    practitioners: [createPractitionerDetails(practitionerData)],
    chargeItems: invoiceData.treatments.map((invoice) => {
      return createChargeItems(invoice.treatment, invoice.quantity,invoice._id);
    }),
    invoice: createInvoiceDetails(
      invoiceData._id,
      "issued",
      new Date().toISOString().split("T")[0],
      { value: invoiceData.totalNet, currency: "INR" },
      { value: invoiceData.totalGross, currency: "INR" },
      invoiceData
    ),
    encounter: createEncounterDetails(),
    organization: createOrganizationDetails(clinicData),
    documentReferences: [],
    signature: createSignatureDetails("", [
      `${practitionerData.firstName + practitionerData.lastName}`,
    ]),
  };
};

export const bundleFhir = async (req, res) => {
  try {
    let query = req.query;
    let prescription = await getPrescriptionAndVitalsForAppointment(
      query.clinicId,
      query.appointmentId
    );
    let medicalHistory = await getMedicalHistoryForPatient(query.patientId);
    let clinicData = await getClientById(query.clinicId);
    let patientData = await getPatientDetail(query.patientId);
    let appointmentData = await getAppointment(query.appointmentId);
    let invoiceData = await Invoice.findOne({
      appointmentId: query.appointmentId,
    });
    let practitionerData = await staffById(appointmentData.doctorId);

    // Create all structured data objects
    let opConsultData = await createStructuredData(
      "OPConsultRecord",
      clinicData,
      patientData,
      appointmentData,
      practitionerData,
      prescription,
      medicalHistory
    );

    let invoiceStructureData = await createStructureForInvoice(
      "InvoiceRecord",
      clinicData,
      patientData,
      practitionerData,
      invoiceData
    );

    let prescriptionStructureData = await createPrescriptionStructuredData(
      "PrescriptionRecord",
      clinicData,
      patientData,
      appointmentData,
      practitionerData,
      prescription,
      medicalHistory
    );

    let dischargeSummaryStructureData =
      await createDischargeSummaryStructuredData(
        "DischargeSummaryRecord",
        clinicData,
        patientData,
        appointmentData,
        practitionerData,
        prescription,
        medicalHistory
      );

    let healthDocumentStructureData = await createHealthRecordStructuredData(
      "HealthDocumentRecord",
      clinicData,
      appointmentData,
      patientData,
      practitionerData
    );

    let wellnessDocumentStructureData = createWellnessStructuredData(
      "WellnessRecord",
      patientData,
      practitionerData,
      prescription,
      medicalHistory
    );
    const allStructuredData = {
      opConsult: opConsultData,
      // invoice: invoiceStructureData,
      // prescription: prescriptionStructureData,
      // dischargeSummary: dischargeSummaryStructureData,
      // healthDocument: healthDocumentStructureData,
      // wellness: wellnessDocumentStructureData,
    };
    
    const filePath = path.join("./", "opConsultData.json");
    fs.writeFileSync(filePath, JSON.stringify(invoiceStructureData, null, 2), "utf-8");
    
    // Create array of API call promises
    const apiCalls = Object.entries(allStructuredData).map(
      async ([type, data]) => {
        try {
          const response = await fetch(`${process.env.WHATSAPP_API_URL}/abha/fhir`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
          });
    
          const responseData = await response.json();
          return { type, success: true, data: responseData };
        } catch (error) {
          console.error(`Error calling API for ${type}:`, error);
          return { type, success: false, error: error.message };
        }
      }
    );
    
    // Wait for all API calls to complete
    const results = await Promise.all(apiCalls);
    
    // Process results
    results.forEach(result => {
      if (result.success) {
        console.log(`Success for ${result.type}:`, result.data);
      } else {
        console.error(`Failed for ${result.type}:`, result.error);
      }
    });
    // Wait for all API calls to complete
    // const results = await Promise.all(apiCalls);

    if(results&&!patientData.linkToken){
      const abhaNumber = patientData.abhaNumber.replace(/-/g, "");
      const requestBody = {
        abhaNumber: abhaNumber || null,
        abhaAddress: patientData.abhaAddress || null,
        name: `${patientData.firstName} ${patientData.lastName}`,
        gender: patientData.gender[0],
        yearOfBirth: new Date(patientData.birthday).getFullYear(),
      };
      const response = await fetch(`${process.env.WHATSAPP_API_URL}/abha/m2/generate-linking-token`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-hip-id":"IN2410000949",
          "x-cm-id":"sbx"
        },
        body: JSON.stringify(requestBody),
      });
      const responseData = await response.json();
      console.log("responseData",responseData);
      // return { type, success: true, data: responseData };
    }


console.log("================================",results)


    // Return results to client
    res.status(200).json({
      message: "Bundle FHIR operations completed",
      results,
    });





  } catch (error) {
    console.error("Error in bundleFhir:", error);
    res.status(500).json({ error: error.message });
  }
};
