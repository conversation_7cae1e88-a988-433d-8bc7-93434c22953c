import moment from "moment";
export function findIndexInArray(myArray,searchTerm){
    let index = -1;
    for(let i = 0, len = myArray.length; i < len; i++) {
        if (myArray[i].clinicId === searchTerm) {
            index = i;
            return index;
        }
    }
}
export const generateDateRange = (startDate, endDate) => {
    const dates = [];
    let currentDate = new Date(startDate);
    const endDate_obj = new Date(endDate);

    while (currentDate <= endDate_obj) {
      dates.push(moment(currentDate).format('YYYY-MM-DD'));
      currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000); // Add one day

      // Safety check to prevent infinite loop
      if (dates.length > 10000) {
        console.warn('Date range too large, breaking loop to prevent infinite iteration');
        break;
      }
    }

    return dates;
  };