J

javascriptS3504)Unexpected var, use let or const instead.2 @J

javascriptS3504)Unexpected var, use let or const instead.2 @V

javascriptS14817Remove the declaration of the unused 'result' variable.2 S

javascriptS18544Remove this useless assignment to variable "result".2 H

javascriptS3504)Unexpected var, use let or const instead.2(( <

javascriptS2814'message' is already defined.2(( J

javascriptS3504)Unexpected var, use let or const instead.233 @V

javascriptS14817Remove the declaration of the unused 'result' variable.233 S

javascriptS18544Remove this useless assignment to variable "result".233 